#!/usr/bin/env python3 """ Script to remove all emojis from all files in the CALI project """ import os import re import glob def remove_emojis_from_text(text): """Remove emojis from text using regex patterns""" # Common emoji patterns emoji_patterns = [ r'', r'', r'', r'', r'', r'', r'', r'', r'', r'', r'', r'', r'', r'', r'', r'', r'', r'', r'', r'', r'', r'', r'', r'', r'', r'', r'', r'', r'', r'', r'', r'', r'', r'', r'', r'', r'', r'', r'', r'', r'', r'', r'', r'', r'', r'', r'', r'', r'', r'', r'', r'', r'', r'', r'', r'', r'', r'', r'', r'', r'', r'', r'', r'', r'', r'', r'', r'', r'', r'', r'' ] # Remove each emoji pattern for pattern in emoji_patterns: text = re.sub(pattern, '', text) # General emoji removal using Unicode ranges # This covers most emojis emoji_pattern = re.compile( "[" "\U0001F600-\U0001F64F" # emoticons "\U0001F300-\U0001F5FF" # symbols & pictographs "\U0001F680-\U0001F6FF" # transport & map symbols "\U0001F1E0-\U0001F1FF" # flags (iOS) "\U00002702-\U000027B0" # dingbats "\U000024C2-\U0001F251" "]+", flags=re.UNICODE ) text = emoji_pattern.sub('', text) # Clean up extra spaces that might be left text = re.sub(r'\s+', ' ', text) text = re.sub(r'^\s+|\s+$', '', text, flags=re.MULTILINE) return text def process_file(filepath): """Process a single file to remove emojis""" try: with open(filepath, 'r', encoding='utf-8') as f: content = f.read() original_content = content cleaned_content = remove_emojis_from_text(content) if cleaned_content != original_content: with open(filepath, 'w', encoding='utf-8') as f: f.write(cleaned_content) print(f" Cleaned emojis from: {filepath}") return True else: print(f"- No emojis found in: {filepath}") return False except Exception as e: print(f" Error processing {filepath}: {e}") return False def main(): """Main function to process all files""" print("🧹 CALI V2 Emoji Removal Tool") print("=" * 50) # File patterns to process file_patterns = [ "*.py", "*.md", "*.json", "*.csv", "*.txt", "*.yaml", "*.yml" ] # Directories to skip skip_dirs = { "__pycache__", ".git", "node_modules", "venv", "env", ".pytest_cache", "alembic" } files_processed = 0 files_cleaned = 0 for pattern in file_patterns: for filepath in glob.glob(pattern, recursive=False): # Skip if in excluded directory if any(skip_dir in filepath for skip_dir in skip_dirs): continue if process_file(filepath): files_cleaned += 1 files_processed += 1 print("\n" + "=" * 50) print(f" Summary:") print(f" Files processed: {files_processed}") print(f" Files cleaned: {files_cleaned}") print(f" Files unchanged: {files_processed - files_cleaned}") print("\n Emoji removal complete!") if __name__ == "__main__": main()