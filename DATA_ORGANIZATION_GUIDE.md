# Student Behavior Data Organization Guide ## Overview This guide explains how to organize your student behavior data for the Student Behavior Analysis and Forecasting application. ## Required Data Format ### File Format - **File Type**: CSV (Comma Separated Values) - **Encoding**: UTF-8 - **File Extension**: `.csv` ### Data Structure Requirements #### 1. Header Rows (First 3 rows) The application expects **exactly 3 header rows** before the actual data: ```csv Row 1: Metadata (can contain summary information - will be skipped) Row 2: Additional metadata (will be skipped) Row 3: Column headers (REQUIRED - contains time slot labels) ``` #### 2. Column Structure Your CSV must have the following column structure: | Column 1 | Column 2 | Column 3 | Column 4 | ... | Column N | |----------|----------|----------|----------|-----|----------| | Day Name | Date | 7:30-7:45 AM | 7:45-8:00 AM | ... | 3:30-3:45 PM | | Tuesday | 8/16/2022 | g | g | ... | g | | Wednesday | 8/17/2022 | y | g | ... | r | **Column Requirements:** - **Column 1**: Day of week (e.g., "Tuesday", "Wednesday") - can be any text - **Column 2**: Date in M/D/YYYY format (e.g., "8/16/2022", "12/25/2022") - **Columns 3+**: Time slots with behavior markers #### 3. Time Slot Columns Time slots should be labeled with times containing: - **AM/PM format**: "7:30-7:45 AM", "12:00-12:15 PM" - **Consistent intervals**: Typically 15-minute intervals - **School day coverage**: Usually 7:30 AM to 3:45 PM #### 4. Behavior Markers Each time slot should contain one of these values: | Marker | Meaning | Numeric Value | |--------|---------|---------------| | `g` or `G` | Green - Meeting expectations | 2 | | `y` | Yellow - Needs some support | 1 | | `r` | Red - Needs significant support | 0 | | `a` | Absent | (ignored) | | `0` or empty | No data | (ignored) | ## Sample Data Format ```csv ,,Red/Yellow/Green Data,,,,,,,,,,, :,,,,,,,,,,,,, Date,,7:30-7:45 AM,7:45-8:00 AM,8:00-8:15 AM,8:15-8:30 AM,8:30-8:45 AM,8:45-9:00 AM Tuesday,8/16/2022,,,,g,g,g,g,g,g Wednesday,8/17/2022,,,,g,g,g,g,g,g Thursday,8/18/2022,,,,g,g,g,g,r,r Friday,8/19/2022,,,,,,,,, Monday,8/22/2022,,,,g,g,g,g,g,g ``` ## Common Data Issues and Solutions ### Issue 1: Date Format Problems **Problem**: Dates like "8/182022" or "8//19/2022" **Solution**: Use proper format "8/18/2022" or "8/19/2022" ### Issue 2: Missing Time Slots **Problem**: Inconsistent time slot labels **Solution**: Ensure all time slots follow "H:MM-H:MM AM/PM" format ### Issue 3: Invalid Behavior Markers **Problem**: Using numbers or other symbols **Solution**: Use only `g`, `y`, `r`, `a`, or leave empty ### Issue 4: Empty Rows **Problem**: Completely empty rows in data **Solution**: Remove empty rows or ensure they have valid dates ## Data Preparation Checklist ### Before Uploading Your Data: 1. ** File Format** - [ ] File is saved as .csv - [ ] File uses UTF-8 encoding - [ ] No special characters in filename 2. ** Structure** - [ ] Exactly 3 header rows before data - [ ] Column 1: Day names - [ ] Column 2: Dates in M/D/YYYY format - [ ] Columns 3+: Time slots with AM/PM labels 3. ** Data Quality** - [ ] All dates are valid and properly formatted - [ ] Behavior markers are only g, y, r, a, or empty - [ ] Time slots are consistently labeled - [ ] No completely empty rows in the middle of data 4. ** Coverage** - [ ] At least 30 days of data for meaningful analysis - [ ] Consistent time slot coverage across days - [ ] Recent data (within last school year) ## Example Templates ### Minimal Template (5 time slots) ```csv Student Behavior Data,,,,, Metadata Row,,,,, Date,,9:00-9:15 AM,10:00-10:15 AM,11:00-11:15 AM,1:00-1:15 PM,2:00-2:15 PM Monday,1/15/2024,,g,g,y,g,g Tuesday,1/16/2024,,y,g,g,g,r Wednesday,1/17/2024,,g,g,g,g,g ``` ### Full Day Template (33 time slots) ```csv Student Behavior Tracking,,,,,,,,,,, Daily Behavior Report,,,,,,,,,,, Date,,7:30-7:45 AM,7:45-8:00 AM,8:00-8:15 AM,8:15-8:30 AM,8:30-8:45 AM,8:45-9:00 AM,9:00-9:15 AM,9:15-9:30 AM,9:30-9:45 AM,9:45-10:00 AM Monday,1/15/2024,,g,g,g,g,g,g,g,g,g,g Tuesday,1/16/2024,,g,y,y,g,g,g,r,g,g,g ``` ## Troubleshooting ### Application Shows "0 rows after cleaning" **Causes:** - Incorrect date format in Column 2 - Missing or malformed header rows - All behavior data is empty or invalid **Solutions:** 1. Check date format: Use M/D/YYYY (e.g., "1/15/2024") 2. Ensure exactly 3 header rows 3. Verify behavior markers are g, y, r, a, or empty ### Application Shows "NaN behavior scores" **Causes:** - Days with no valid behavior markers - All time slots marked as absent ('a') or empty **Solutions:** 1. Ensure each day has at least some g, y, or r markers 2. Remove or fix days with only absent markers ### Predictions Are Inaccurate **Causes:** - Insufficient data (less than 30 days) - Inconsistent data collection - Too many missing values **Solutions:** 1. Collect at least 30-60 days of consistent data 2. Maintain regular data collection schedule 3. Minimize missing time slots ## Getting Help If you continue to have issues: 1. Check that your data matches the sample format exactly 2. Verify all dates are valid and in the correct format 3. Ensure behavior markers are consistent 4. Try with a smaller sample of your data first The application will provide specific error messages to help identify data formatting issues. ## Quick Start Guide ### Step 1: Generate a Template Run this command to create sample templates: ```bash python data_template_generator.py ``` This creates: - `sample_student_behavior_data.csv` - Example with realistic data - `minimal_behavior_template.csv` - Simple 5-slot template for testing - `blank_behavior_template.csv` - Empty template to fill in ### Step 2: Validate Your Data Before uploading, test your file: ```bash python -c "from data_validator import validate_data_file; is_valid, report = validate_data_file('your_file.csv'); print(report)" ``` ### Step 3: Upload to Application 1. Open the Streamlit application 2. Click "Upload CSV File" 3. Select your validated file 4. Click "Process File" The application will automatically validate your file and show helpful error messages if there are issues. ## Application Features Once your data is properly formatted and uploaded, you can: ### **Behavior Predictions** - Next-day behavior forecasting using machine learning - Time-slot level predictions (Red/Yellow/Green) - Confidence scores and trend analysis ### **Data Analysis** - Behavior pattern visualization - Weekly and seasonal trends - Statistical summaries and metrics ### **Alert System** - Configurable behavior thresholds - Automatic alerts for concerning patterns - Prediction-based early warnings ### **Data Management** - Save processed data to database - Track historical behavior records - Export analysis results ### **Advanced Features** - Medication impact tracking - Environmental factor analysis - Staff change correlation - Seasonal pattern recognition ## Troubleshooting Common Issues ### "No valid date rows found" - Check that Column 2 contains dates in M/D/YYYY format - Ensure dates are not in the first 3 header rows - Verify dates follow the pattern: "Monday,8/16/2022" ### "No time slot columns found" - Time slots must contain both ':' and 'AM' or 'PM' - Example: "7:30-7:45 AM", "12:00-12:15 PM" - Check that time slots are in row 3 (column headers) ### "Very little behavior data found" - Ensure time slot columns contain 'g', 'y', or 'r' values - Check for typos in behavior markers - Verify data is not all empty or marked as absent ('a') ### "XGBoost errors" or "NaN values" - Need at least 30 days of data for reliable predictions - Remove rows where all time slots are empty - Ensure at least some behavior markers exist for each day ## Support For additional help: 1. Check the generated sample files for reference 2. Use the data validator before uploading 3. Review error messages in the application 4. Start with the minimal template for testing The application includes built-in help and validation to guide you through the process!