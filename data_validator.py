import pandas as pd import numpy as np from datetime import datetime import re class DataValidator: """Validates student behavior data format and provides helpful error messages""" def __init__(self, file_path): self.file_path = file_path self.errors = [] self.warnings = [] def validate_file(self): """Comprehensive validation of the data file""" self.errors = [] self.warnings = [] # Check file existence and readability if not self._check_file_exists(): return False # Load and validate structure if not self._load_and_validate_structure(): return False # Validate dates if not self._validate_dates(): return False # Validate time slots if not self._validate_time_slots(): return False # Validate behavior data if not self._validate_behavior_data(): return False return True def _check_file_exists(self): """Check if file exists and is readable""" try: with open(self.file_path, 'r') as f: f.read(100) # Try to read first 100 characters return True except FileNotFoundError: self.errors.append(f" File not found: {self.file_path}") return False except Exception as e: self.errors.append(f" Cannot read file: {str(e)}") return False def _load_and_validate_structure(self): """Load CSV and validate basic structure""" try: # Try to load with different approaches self.raw_data = pd.read_csv(self.file_path) # Check if we have enough rows (at least 3 header + some data) if len(self.raw_data) < 5: self.errors.append( " File has too few rows. Need at least 3 header rows + data rows.\n" "Expected format:\n" "Row 1: Metadata\n" "Row 2: Metadata\n" "Row 3: Column headers\n" "Row 4+: Data rows" ) return False # Load data properly (skip first 2 rows) self.data = pd.read_csv(self.file_path, skiprows=2) if len(self.data.columns) < 3: self.errors.append( " Not enough columns. Need at least 3 columns:\n" "Column 1: Day names (Monday, Tuesday, etc.)\n" "Column 2: Dates (M/D/YYYY format)\n" "Column 3+: Time slots (7:30-7:45 AM, etc.)" ) return False return True except pd.errors.EmptyDataError: self.errors.append(" CSV file is empty") return False except Exception as e: self.errors.append(f" Error reading CSV: {str(e)}") return False def _validate_dates(self): """Validate date column format""" # Find the date column (usually second column) date_column = None for col in self.data.columns[:3]: # Check first 3 columns sample_values = self.data[col].dropna().astype(str).head(10) if any('/' in str(val) and any(c.isdigit() for c in str(val)) for val in sample_values): date_column = col break if date_column is None: self.errors.append( " No date column found!\n" "Expected: Second column should contain dates in M/D/YYYY format\n" "Examples: 8/16/2022, 12/25/2022, 1/5/2023" ) return False # Validate date formats date_values = self.data[date_column].dropna().astype(str) valid_dates = 0 invalid_examples = [] for i, date_str in enumerate(date_values.head(20)): # Check first 20 dates if self._is_valid_date_format(date_str): valid_dates += 1 else: if len(invalid_examples) < 3: # Collect up to 3 examples invalid_examples.append(f"Row {i+4}: '{date_str}'") if valid_dates == 0: self.errors.append( " No valid dates found!\n" f"Invalid date examples: {', '.join(invalid_examples)}\n" "Expected format: M/D/YYYY (e.g., 8/16/2022, 12/25/2022)" ) return False elif len(invalid_examples) > 0: self.warnings.append( f" Some invalid dates found: {', '.join(invalid_examples)}\n" "These rows will be skipped during processing." ) return True def _validate_time_slots(self): """Validate time slot columns""" time_slots = [col for col in self.data.columns if ':' in col and ('AM' in col or 'PM' in col)] if len(time_slots) == 0: self.errors.append( " No time slot columns found!\n" "Expected: Columns with time ranges like '7:30-7:45 AM', '12:00-12:15 PM'\n" "Make sure your time columns include 'AM' or 'PM'" ) return False if len(time_slots) < 5: self.warnings.append( f" Only {len(time_slots)} time slots found. " "Consider having more time slots for better analysis." ) # Check for consistent time format invalid_time_formats = [] for slot in time_slots[:5]: # Check first 5 if not re.match(r'\d{1,2}:\d{2}.*\d{1,2}:\d{2}.*(AM|PM)', slot): invalid_time_formats.append(slot) if invalid_time_formats: self.warnings.append( f" Unusual time slot formats: {', '.join(invalid_time_formats)}\n" "Expected format: 'H:MM-H:MM AM/PM'" ) return True def _validate_behavior_data(self): """Validate behavior marker data""" time_slots = [col for col in self.data.columns if ':' in col and ('AM' in col or 'PM' in col)] if not time_slots: return True # Already handled in time slot validation # Check behavior markers all_values = [] for slot in time_slots: values = self.data[slot].dropna().astype(str).str.lower() all_values.extend(values.tolist()) valid_markers = {'g', 'y', 'r', 'a', '0', '', 'nan'} invalid_markers = set(all_values) - valid_markers if invalid_markers: # Remove common non-issues invalid_markers = {m for m in invalid_markers if m not in ['', 'nan', '0']} if invalid_markers: examples = list(invalid_markers)[:5] # Show up to 5 examples self.warnings.append( f" Unexpected behavior markers found: {', '.join(examples)}\n" "Expected markers: 'g' (green), 'y' (yellow), 'r' (red), 'a' (absent), or empty" ) # Check for sufficient data valid_behavior_count = sum(1 for val in all_values if val in {'g', 'y', 'r'}) total_slots = len(all_values) if total_slots > 0: data_coverage = valid_behavior_count / total_slots if data_coverage < 0.1: self.warnings.append( f" Very little behavior data found ({data_coverage:.1%} coverage). " "Make sure your time slot columns contain 'g', 'y', or 'r' values." ) return True def _is_valid_date_format(self, date_str): """Check if a string represents a valid date""" date_str = str(date_str).strip() # Handle combined format like "Tuesday,8/16/2022" if ',' in date_str: date_str = date_str.split(',')[1].strip() # Fix double slashes date_str = date_str.replace('//', '/') # Check basic format if not re.match(r'\d{1,2}/\d{1,2}/\d{4}', date_str): return False # Try to parse try: pd.to_datetime(date_str, format='%m/%d/%Y') return True except: return False def get_validation_report(self): """Get a formatted validation report""" report = [] if not self.errors and not self.warnings: report.append(" Data validation passed! Your file looks good.") return "\n".join(report) if self.errors: report.append(" ERRORS (must be fixed):") for error in self.errors: report.append(f" {error}") report.append("") if self.warnings: report.append(" WARNINGS (recommended to fix):") for warning in self.warnings: report.append(f" {warning}") report.append("") if self.errors: report.append(" Please fix the errors above before using the application.") report.append(" See DATA_ORGANIZATION_GUIDE.md for detailed formatting instructions.") else: report.append(" No critical errors found. You can proceed with warnings.") return "\n".join(report) def validate_data_file(file_path): """Quick validation function""" validator = DataValidator(file_path) is_valid = validator.validate_file() report = validator.get_validation_report() return is_valid, report