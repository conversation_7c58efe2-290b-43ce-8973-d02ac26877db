import pandas as pd
from datetime import datetime, timedelta
import os

def generate_sample_data_template(output_file="sample_student_behavior_data.csv", num_days=30):
    """
    Generate a sample CSV file with proper formatting for student behavior data
    
    Args:
        output_file (str): Name of the output CSV file
        num_days (int): Number of days of sample data to generate
    """
    
    # Create header rows
    header_row_1 = "Student Behavior Tracking Data"
    header_row_2 = "Daily Behavior Analysis Report"
    
    # Define time slots (typical school day)
    time_slots = [
        "7:30-7:45 AM", "7:45-8:00 AM", "8:00-8:15 AM", "8:15-8:30 AM", "8:30-8:45 AM",
        "8:45-9:00 AM", "9:00-9:15 AM", "9:15-9:30 AM", "9:30-9:45 AM", "9:45-10:00 AM",
        "10:00-10:15 AM", "10:15-10:30 AM", "10:30-10:45 AM", "10:45-11:00 AM", "11:00-11:15 AM",
        "11:15-11:30 AM", "11:30-11:45 AM", "11:45-12:00 PM", "12:00-12:15 PM", "12:15-12:30 PM",
        "12:30-12:45 PM", "12:45-1:00 PM", "1:00-1:15 PM", "1:15-1:30 PM", "1:30-1:45 PM",
        "1:45-2:00 PM", "2:00-2:15 PM", "2:15-2:30 PM", "2:30-2:45 PM", "2:45-3:00 PM",
        "3:00-3:15 PM", "3:15-3:30 PM", "3:30-3:45 PM"
    ]
    
    # Create column headers
    columns = ["Date"] + time_slots
    
    # Generate sample data
    data_rows = []
    start_date = datetime.now() - timedelta(days=num_days)
    
    day_names = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"]
    behavior_options = ["g", "y", "r", ""]  # Green, Yellow, Red, Empty
    
    for i in range(num_days):
        current_date = start_date + timedelta(days=i)
        
        # Skip weekends for school data (optional)
        if current_date.weekday() >= 5:  # Saturday = 5, Sunday = 6
            continue
            
        day_name = day_names[current_date.weekday()]
        date_str = f"{day_name},{current_date.strftime('%m/%d/%Y')}"
        
        # Generate sample behavior data
        # Most slots are green (good behavior), some yellow, few red
        behavior_data = []
        for slot in time_slots:
            # Simulate realistic behavior patterns
            import random
            rand = random.random()
            if rand < 0.7:  # 70% green
                behavior = "g"
            elif rand < 0.9:  # 20% yellow
                behavior = "y"
            elif rand < 0.95:  # 5% red
                behavior = "r"
            else:  # 5% empty (absent/no data)
                behavior = ""
            behavior_data.append(behavior)
        
        row = [date_str] + behavior_data
        data_rows.append(row)
    
    # Create the complete CSV content
    csv_content = []
    
    # Add header rows
    csv_content.append([header_row_1] + [""] * len(time_slots))
    csv_content.append([header_row_2] + [""] * len(time_slots))
    csv_content.append(columns)  # Column headers
    
    # Add data rows
    csv_content.extend(data_rows)
    
    # Create DataFrame and save
    df = pd.DataFrame(csv_content)
    df.to_csv(output_file, index=False, header=False)
    
    print(f"✅ Sample data template created: {output_file}")
    print(f"📊 Generated {len(data_rows)} days of sample behavior data")
    print(f"🕐 Time slots: {len(time_slots)} (15-minute intervals)")
    print(f"📅 Date range: {data_rows[0][0].split(',')[1]} to {data_rows[-1][0].split(',')[1]}")
    print("\n📖 This file demonstrates the proper format for your student behavior data.")
    print("   Replace the sample data with your actual behavior observations.")

def generate_minimal_template(output_file="minimal_behavior_template.csv", num_days=7):
    """Generate a minimal template with fewer time slots for testing"""
    
    # Minimal time slots for testing
    time_slots = [
        "9:00-9:15 AM", "10:00-10:15 AM", "11:00-11:15 AM", 
        "1:00-1:15 PM", "2:00-2:15 PM"
    ]
    
    # Create header rows
    header_row_1 = "Student Behavior Data - Minimal Template"
    header_row_2 = "5 Time Slots for Testing"
    
    # Create column headers
    columns = ["Date"] + time_slots
    
    # Generate sample data
    data_rows = []
    start_date = datetime.now() - timedelta(days=num_days)
    
    day_names = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"]
    
    for i in range(num_days):
        current_date = start_date + timedelta(days=i)
        
        # Skip weekends
        if current_date.weekday() >= 5:
            continue
            
        day_name = day_names[current_date.weekday()]
        date_str = f"{day_name},{current_date.strftime('%m/%d/%Y')}"
        
        # Simple pattern: mostly green with occasional yellow
        behavior_data = ["g", "g", "y", "g", "g"]
        
        row = [date_str] + behavior_data
        data_rows.append(row)
    
    # Create the complete CSV content
    csv_content = []
    csv_content.append([header_row_1] + [""] * len(time_slots))
    csv_content.append([header_row_2] + [""] * len(time_slots))
    csv_content.append(columns)
    csv_content.extend(data_rows)
    
    # Create DataFrame and save
    df = pd.DataFrame(csv_content)
    df.to_csv(output_file, index=False, header=False)
    
    print(f"✅ Minimal template created: {output_file}")
    print(f"📊 Generated {len(data_rows)} days with {len(time_slots)} time slots each")

def create_blank_template(output_file="blank_behavior_template.csv", num_days=30):
    """Create a blank template for users to fill in their own data"""
    
    # Standard time slots
    time_slots = [
        "7:30-7:45 AM", "7:45-8:00 AM", "8:00-8:15 AM", "8:15-8:30 AM", "8:30-8:45 AM",
        "8:45-9:00 AM", "9:00-9:15 AM", "9:15-9:30 AM", "9:30-9:45 AM", "9:45-10:00 AM",
        "10:00-10:15 AM", "10:15-10:30 AM", "10:30-10:45 AM", "10:45-11:00 AM", "11:00-11:15 AM",
        "11:15-11:30 AM", "11:30-11:45 AM", "11:45-12:00 PM", "12:00-12:15 PM", "12:15-12:30 PM",
        "12:30-12:45 PM", "12:45-1:00 PM", "1:00-1:15 PM", "1:15-1:30 PM", "1:30-1:45 PM",
        "1:45-2:00 PM", "2:00-2:15 PM", "2:15-2:30 PM", "2:30-2:45 PM", "2:45-3:00 PM",
        "3:00-3:15 PM", "3:15-3:30 PM", "3:30-3:45 PM"
    ]
    
    # Create header rows
    header_row_1 = "Student Behavior Tracking Template"
    header_row_2 = "Fill in: g=Green(good), y=Yellow(needs support), r=Red(significant support), a=Absent"
    
    # Create column headers
    columns = ["Date"] + time_slots
    
    # Generate blank data rows with proper date format examples
    data_rows = []
    start_date = datetime.now()
    
    day_names = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"]
    
    for i in range(min(num_days, 10)):  # Limit to 10 example rows
        current_date = start_date + timedelta(days=i)
        
        # Skip weekends
        if current_date.weekday() >= 5:
            continue
            
        day_name = day_names[current_date.weekday()]
        date_str = f"{day_name},{current_date.strftime('%m/%d/%Y')}"
        
        # Create empty row
        behavior_data = [""] * len(time_slots)
        
        row = [date_str] + behavior_data
        data_rows.append(row)
    
    # Create the complete CSV content
    csv_content = []
    csv_content.append([header_row_1] + [""] * len(time_slots))
    csv_content.append([header_row_2] + [""] * len(time_slots))
    csv_content.append(columns)
    csv_content.extend(data_rows)
    
    # Create DataFrame and save
    df = pd.DataFrame(csv_content)
    df.to_csv(output_file, index=False, header=False)
    
    print(f"✅ Blank template created: {output_file}")
    print(f"📝 Template has {len(data_rows)} example date rows to fill in")
    print(f"🕐 {len(time_slots)} time slots from 7:30 AM to 3:45 PM")
    print("\n📝 Instructions:")
    print("   1. Fill in the behavior markers: g, y, r, a, or leave empty")
    print("   2. Add more date rows following the same format")
    print("   3. Keep the first 3 rows as headers")

if __name__ == "__main__":
    print("Student Behavior Data Template Generator")
    print("=" * 50)
    
    # Generate all templates
    generate_sample_data_template()
    print()
    generate_minimal_template()
    print()
    create_blank_template()
    
    print("\n🎉 All templates generated successfully!")
    print("\n📖 See DATA_ORGANIZATION_GUIDE.md for detailed instructions.")
